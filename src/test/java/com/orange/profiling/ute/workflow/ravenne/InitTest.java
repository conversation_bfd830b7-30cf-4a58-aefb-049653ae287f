package com.orange.profiling.ute.workflow.ravenne;

import static org.junit.Assert.*;

import java.io.File;
import java.io.FileInputStream;
import java.net.URI;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.cli.ParseException;
import org.apache.log4j.BasicConfigurator;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.joda.time.DateTime;
import org.joda.time.DateTimeFieldType;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.junit.Before;
import org.junit.Test;

import com.orange.profiling.common.utils.oozie.OozieProperties;

public class InitTest {

    private static final String YEAR_WEEK_SCHEMA = "(\\d{4})\\/(\\d{1,2})";
    private static final Pattern YEAR_WEEK_PATTERN = Pattern.compile(YEAR_WEEK_SCHEMA);
    private static final String DATE_YYYYMMDD_SCHEMA = "yyyyMMdd";
    private static final DateTimeFormatter DATE_YYYYMMDD_FORMAT = DateTimeFormat.forPattern(DATE_YYYYMMDD_SCHEMA);

    @Before
    public void setUp() {
        BasicConfigurator.configure();
        Logger.getRootLogger().setLevel(Level.ERROR);
    }

    @Test
    public void testMain() throws Exception {
        URI resourcePath = this.getClass().getResource("/").toURI();
        String ooziePropertiesPath = resourcePath.resolve("oozie-properties/oozieprops.properties").getPath();
        String gdprFlagFilePath = resourcePath.resolve("gdpr-flag-test.properties").getPath();
        System.setProperty(OozieProperties.OOZIE_ACTION_OUTPUT_PROPERTIES, ooziePropertiesPath);


        String[] args = {
                "-paramDate", "20200128",
                "-filterLimit", "3",
                "-gdprFlagFilePath", gdprFlagFilePath
        };

        Init.setNow(new DateTime(2020, 01, 30, 9, 21, 55, 351));
        Init.main(args);

        Properties props = new Properties();
        props.load(new FileInputStream(new File(ooziePropertiesPath)));

        assertPropertyEquals("2020/5", "endYearAndWeek", props);
        assertPropertyEquals("2020/4", "endPrevYearAndWeek", props);
        assertPropertyEquals("2020/3", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2019/46", "beginYearAndWeek", props);
        assertPropertyEquals("1580372515", "startTs", props);
        assertPropertyEquals("1580199715", "vStart", props);
    }

    @Test
    public void testMainOkDefaultFilterLimitIs2() throws Exception {
        URI resourcePath = this.getClass().getResource("/").toURI();
        String ooziePropertiesPath = resourcePath.resolve("oozie-properties/oozieprops.properties").getPath();
        String gdprFlagFilePath = resourcePath.resolve("gdpr-flag-test.properties").getPath();
        System.setProperty(OozieProperties.OOZIE_ACTION_OUTPUT_PROPERTIES, ooziePropertiesPath);


        String[] args = {
                "-paramDate", "20200128",
                "-filterLimit", "",
                "-gdprFlagFilePath", gdprFlagFilePath
        };

        Init.setNow(new DateTime(2020, 01, 30, 9, 21, 55, 351));
        Init.main(args);

        Properties props = new Properties();
        props.load(new FileInputStream(new File(ooziePropertiesPath)));

        assertPropertyEquals("2020/5", "endYearAndWeek", props);
        assertPropertyEquals("2020/4", "endPrevYearAndWeek", props);
        assertPropertyEquals("2020/4", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2019/46", "beginYearAndWeek", props);
        assertPropertyEquals("20200121", "oneWeekAgoDate", props);
        assertPropertyEquals("20191224", "fiveWeekAgoDate", props);
        assertPropertyEquals("1580372515", "startTs", props);
        assertPropertyEquals("1580199715", "vStart", props);
    }

    @Test
    public void testMainOkWhenBadFilterLimitDefaultIs2() throws Exception {
        URI resourcePath = this.getClass().getResource("/").toURI();
        String ooziePropertiesPath = resourcePath.resolve("oozie-properties/oozieprops.properties").getPath();
        String gdprFlagFilePath = resourcePath.resolve("gdpr-flag-test.properties").getPath();
        System.setProperty(OozieProperties.OOZIE_ACTION_OUTPUT_PROPERTIES, ooziePropertiesPath);


        String[] args = {
                "-paramDate", "20200128",
                "-filterLimit", "B3",
                "-gdprFlagFilePath", gdprFlagFilePath
        };

        Init.setNow(new DateTime(2020, 01, 30, 9, 21, 55, 351));
        Init.main(args);

        Properties props = new Properties();
        props.load(new FileInputStream(new File(ooziePropertiesPath)));

        assertPropertyEquals("2020/5", "endYearAndWeek", props);
        assertPropertyEquals("2020/4", "endPrevYearAndWeek", props);
        assertPropertyEquals("2020/4", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2019/46", "beginYearAndWeek", props);
        assertPropertyEquals("20200121", "oneWeekAgoDate", props);
        assertPropertyEquals("20191224", "fiveWeekAgoDate", props);
        assertPropertyEquals("1580372515", "startTs", props);
        assertPropertyEquals("1580199715", "vStart", props);
    }

    @Test
    public void testMainOkMaxFilterLimitIs12() throws Exception {
        URI resourcePath = this.getClass().getResource("/").toURI();
        String ooziePropertiesPath = resourcePath.resolve("oozie-properties/oozieprops.properties").getPath();
        String gdprFlagFilePath = resourcePath.resolve("gdpr-flag-test.properties").getPath();
        System.setProperty(OozieProperties.OOZIE_ACTION_OUTPUT_PROPERTIES, ooziePropertiesPath);


        String[] args = {
                "-paramDate", "20200128",
                "-filterLimit", "13",
                "-gdprFlagFilePath", gdprFlagFilePath
        };

        Init.setNow(new DateTime(2020, 01, 30, 9, 21, 55, 351));
        Init.main(args);

        Properties props = new Properties();
        props.load(new FileInputStream(new File(ooziePropertiesPath)));

        assertPropertyEquals("2020/5", "endYearAndWeek", props);
        assertPropertyEquals("2020/4", "endPrevYearAndWeek", props);
        assertPropertyEquals("2019/46", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2019/46", "beginYearAndWeek", props);
        assertPropertyEquals("20200121", "oneWeekAgoDate", props);
        assertPropertyEquals("20191224", "fiveWeekAgoDate", props);
        assertPropertyEquals("1580372515", "startTs", props);
        assertPropertyEquals("1580199715", "vStart", props);
    }

    @Test
    public void testMainKo() throws Exception {
        URI resourcePath = this.getClass().getResource("/").toURI();
        String ooziePropertiesPath = resourcePath.resolve("oozie-properties/oozieprops.properties").getPath();
        System.setProperty(OozieProperties.OOZIE_ACTION_OUTPUT_PROPERTIES, ooziePropertiesPath);


        String[] args = {
                "-paramDate", "",
                "-filterLimit", "",
                "-gdprFlagFilePath", ""
        };
        Init.setNow(new DateTime(2020, 01, 30, 9, 21, 55, 351));

        try {
            Init.main(args);
        }
        catch(ParseException e) {
            assertEquals("paramDate parameter not found",  e.getMessage());
        }
    }

    @Test
    public void testGetWorkingDate() {
        DateTime workingDate = Init.getWorkingDate("20190318");
        assertEquals(2019, workingDate.get(DateTimeFieldType.year()));
        assertEquals(03, workingDate.get(DateTimeFieldType.monthOfYear()));
        assertEquals(18, workingDate.get(DateTimeFieldType.dayOfMonth()));
    }

    @Test
    public void testCalculateOozieDatesStandard() {
        DateTime workingDate = new DateTime(2018, 10, 15, 5, 0, 0, 0); // This is a Monday of week 42
        Init.setNow(new DateTime(2018, 10, 20, 15, 45, 55, 351));
        Properties props = Init.calculateOozieDates(workingDate,2);

        assertPropertyEquals("2018/42", "endYearAndWeek", props);
        assertPropertyEquals("2018/41", "endPrevYearAndWeek", props);
        assertPropertyEquals("2018/41", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2018/31", "beginYearAndWeek", props);
        assertPropertyEquals("20181008", "oneWeekAgoDate", props);
        assertPropertyEquals("20180910", "fiveWeekAgoDate", props);
        assertPropertyEquals("1540043155", "startTs", props);
        assertPropertyEquals("1539611155", "vStart", props);
    }

    @Test
    public void testCalculateOozieDatesFirstWeekOfYear2018() {
        DateTime workingDate = new DateTime(2018, 01, 01, 5, 0, 0, 0); // This is a Monday
        Init.setNow(new DateTime(2018, 01, 05, 15, 45, 55, 351));
        Properties props = Init.calculateOozieDates(workingDate,2);

        assertPropertyEquals("2018/1", "endYearAndWeek", props);
        assertPropertyEquals("2017/52", "endPrevYearAndWeek", props);
        assertPropertyEquals("2017/52", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2017/42", "beginYearAndWeek", props);
        assertPropertyEquals("20171225", "oneWeekAgoDate", props);
        assertPropertyEquals("20171127", "fiveWeekAgoDate", props);
        assertPropertyEquals("1515163555", "startTs", props);
        assertPropertyEquals("1514817955", "vStart", props);
    }

    @Test
    public void testCalculateOozieDatesFirstWeekOfYear2019() {
        DateTime workingDate = new DateTime(2018, 12, 31, 5, 0, 0, 0); // This is a Monday
        Init.setNow(new DateTime(2018, 12, 31, 15, 45, 55, 351));
        Properties props = Init.calculateOozieDates(workingDate,2);

        assertPropertyEquals("2019/1", "endYearAndWeek", props);
        assertPropertyEquals("2018/52", "endPrevYearAndWeek", props);
        assertPropertyEquals("2018/52", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2018/42", "beginYearAndWeek", props);
        assertPropertyEquals("20181224", "oneWeekAgoDate", props);
        assertPropertyEquals("20181126", "fiveWeekAgoDate", props);
        assertPropertyEquals("1546267555", "startTs", props);
        assertPropertyEquals("1546267555", "vStart", props);
    }

    @Test
    public void testCalculateOozieDateslastThursdayOfYear2020() {
        DateTime workingDate = new DateTime(2020, 12, 31, 5, 0, 0, 0); // This is a Thursday
        Init.setNow(new DateTime(2021, 01, 05, 15, 45, 55, 351));
        Properties props = Init.calculateOozieDates(workingDate,2);

        assertPropertyEquals("2020/53", "endYearAndWeek", props);
        assertPropertyEquals("2020/52", "endPrevYearAndWeek", props);
        assertPropertyEquals("2020/52", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2020/42", "beginYearAndWeek", props);
        assertPropertyEquals("20201224", "oneWeekAgoDate", props);
        assertPropertyEquals("20201126", "fiveWeekAgoDate", props);
        assertPropertyEquals("1609857955", "startTs", props);
        assertPropertyEquals("1609425955", "vStart", props);
    }

    @Test
    public void testCalculateOozieDates11WeeksAfterlastThursdayOfYear2020() {
        DateTime workingDate = new DateTime(2021, 3, 18, 5, 0, 0, 0); // 11 weeks after 31/12/2020
        Init.setNow(new DateTime(2021, 03, 20, 15, 45, 55, 351));

        // filterLimit = 14 : we also test the filterLimit is max 12
        // so filterBeginYearAndWeek is never before beginYearAndWeek
        Properties props = Init.calculateOozieDates(workingDate,14);

        assertPropertyEquals("2021/11", "endYearAndWeek", props);
        assertPropertyEquals("2021/10", "endPrevYearAndWeek", props);
        assertPropertyEquals("2020/53", "filterBeginYearAndWeek", props);
        assertPropertyEquals("2020/53", "beginYearAndWeek", props);
        assertPropertyEquals("20210311", "oneWeekAgoDate", props);
        assertPropertyEquals("20210211", "fiveWeekAgoDate", props);
        assertPropertyEquals("1616251555", "startTs", props);
        assertPropertyEquals("1616078755", "vStart", props);
    }

    private void assertPropertyEquals(String expected, String name, Properties props) {
        assertEquals(name, expected, props.get(name));
    }

    @Test
    public void testDateRange() {
        StringBuffer allErrors = new StringBuffer();
        DateTime workingDate = new DateTime().minusMonths(1);
        DateTime endDate =  workingDate.plusYears(5);
        Init.setNow(endDate);
        Properties previousProps = null;

        boolean everythingIsFine = true;

        while(workingDate.isBefore(endDate)) {
            Properties props = Init.calculateOozieDates(workingDate,2);
            if (previousProps != null) {
                String errors = compareProperties(previousProps, props);
                if (!"".equals(errors)) {
                    allErrors.append("Errors for test date "+DATE_YYYYMMDD_FORMAT.print(workingDate)+"\n"+errors);
                    everythingIsFine = false;
                }
            }
            previousProps = props;
            workingDate = workingDate.plusDays(1);
        }

        assertTrue(allErrors.toString(), everythingIsFine);

    }

    private String compareProperties(Properties previous, Properties current) {
        StringBuffer allErrors = new StringBuffer();
        allErrors.append(compareWeek(previous, current, "endYearAndWeek"));
        allErrors.append(compareWeek(previous, current, "endPrevYearAndWeek"));
        allErrors.append(compareWeek(previous, current, "filterBeginYearAndWeek"));
        allErrors.append(compareWeek(previous, current, "beginYearAndWeek"));
        allErrors.append(compareDay(previous, current, "oneWeekAgoDate"));
        allErrors.append(compareDay(previous, current, "fiveWeekAgoDate"));
        return allErrors.toString();
    }

    private String compareWeek(Properties previous, Properties current, String name) {
        String prevWeekProp = previous.getProperty(name);
        Matcher prevMatch = YEAR_WEEK_PATTERN.matcher(prevWeekProp);
        String prevYear = "";
        String prevWeek = "";
        if (prevMatch.matches()) {
            prevYear = prevMatch.group(1);
            prevWeek = prevMatch.group(2);
        }



        String curWeekProp = current.getProperty(name);
        Matcher curMatch = YEAR_WEEK_PATTERN.matcher(curWeekProp);
        String curYear = "";
        String curWeek = "";
        if (curMatch.find()) {
            curYear = curMatch.group(1);
            curWeek = curMatch.group(2);
        }

        if (curYear.matches(prevYear)) {
            if (curWeek.equals(prevWeek)) {
                return "";
            }
            Integer prevWeekInt = Integer.valueOf(prevWeek);
            Integer curWeekInt = Integer.valueOf(curWeek);
            if (curWeekInt == prevWeekInt + 1) {
                return "";
            }
        }
        else {
            Integer prevYearInt = Integer.valueOf(prevYear);
            Integer curYearInt = Integer.valueOf(curYear);
            if (curYearInt == prevYearInt + 1) {
                if ( curWeek.equals("1") && (prevWeek.equals("52") || prevWeek.equals("53")) ) {
                    return "";
                }
            }
        }

        return name+" : prev = "+prevWeekProp+" / cur = "+curWeekProp+"\n";
    }

    private String compareDay(Properties previous, Properties current, String name) {
        String prevDay = previous.getProperty(name);
        String curDay = current.getProperty(name);

        DateTime prevDayDate = DATE_YYYYMMDD_FORMAT.parseDateTime(prevDay);
        DateTime curDayDate = DATE_YYYYMMDD_FORMAT.parseDateTime(curDay);
        DateTime prevDayDatePlusOne = prevDayDate.plusDays(1);
        if (prevDayDatePlusOne.equals(curDayDate)) {
            return "";
        }
        return name+" : prev = "+prevDay+" / cur = "+curDay+"\n";
    }

}
